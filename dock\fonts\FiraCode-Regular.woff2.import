[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://csd848psgx6vc"
path="res://.godot/imported/FiraCode-Regular.woff2-7d758f5997993a7e83304d25f3306213.fontdata"

[deps]

source_file="res://addons/gamedev_assistant/dock/fonts/FiraCode-Regular.woff2"
dest_files=["res://.godot/imported/FiraCode-Regular.woff2-7d758f5997993a7e83304d25f3306213.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
